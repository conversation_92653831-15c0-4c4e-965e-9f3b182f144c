@import "tailwindcss";

/* Custom styles */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Base text colors */
body {
  @apply text-gray-900;
}

/* Text color utilities */
.text-primary {
  @apply text-gray-900;
}

.text-secondary {
  @apply text-gray-600;
}

.text-muted {
  @apply text-gray-500;
}

/* Smooth theme transition */
html {
  transition: background-color 0.3s ease, color 0.3s ease;
}

html.dark {
  color-scheme: dark;
}

/* Animation keyframes for hero blobs */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

/* Animation for loading bars */
@keyframes bars {
  0%, 100% {
    height: 20%;
  }
  50% {
    height: 80%;
  }
}

.animate-bars {
  animation: bars 0.8s ease-in-out infinite;
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

body {
  /* Remove conflicting styles that cause layout issues */
  display: block;
  place-items: unset;
  min-width: unset;
  min-height: unset;
  background-color: #f9fafb;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Remove default button styles that conflicted with Tailwind */
button {
  border-radius: unset;
  border: unset;
  padding: unset;
  font-size: unset;
  font-weight: unset;
  background-color: unset;
  transition: unset;
}

button:hover {
  border-color: unset;
}

button:focus,
button:focus-visible {
  outline: unset;
}

/* Clear default link styles */
a {
  font-weight: unset;
  color: unset;
  text-decoration: unset;
}

a:hover {
  color: unset;
}

@layer base {
  h1 {
    @apply text-2xl font-bold md:text-3xl;
  }
  h2 {
    @apply text-xl font-bold md:text-2xl;
  }
  h3 {
    @apply text-lg font-bold md:text-xl;
  }

  /* Smooth scrolling for entire site */
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn-primary {
    @apply px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-white;
  }
  
  .btn-secondary {
    @apply px-4 py-2 bg-gray-200 text-gray-800 font-medium rounded-lg hover:bg-gray-300 transition-colors shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6 border border-gray-200 transition-colors duration-300;
  }
  
  .input-field {
    @apply w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400 shadow-sm transition-colors duration-300;
  }

  /* Data display components */
  .data-table {
    @apply min-w-full divide-y divide-gray-200 border-gray-200 transition-colors duration-300;
  }
  
  .data-table-header {
    @apply py-3.5 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 transition-colors duration-300;
  }
  
  .data-table-cell {
    @apply px-4 py-4 whitespace-nowrap text-sm text-gray-900 transition-colors duration-300;
  }

  /* Responsive utils */
  .container-responsive {
    @apply w-full px-4 mx-auto sm:max-w-screen-sm md:max-w-screen-md lg:max-w-screen-lg xl:max-w-screen-xl 2xl:max-w-screen-2xl;
  }
  
  .table-responsive {
    @apply w-full overflow-x-auto -mx-4 sm:mx-0 sm:overflow-visible;
  }
  
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }

  /* Animation utilities */
  .transition-standard {
    @apply transition-all duration-300 ease-in-out;
  }

  .hover-lift {
    @apply hover:-translate-y-1 transition-transform duration-300;
  }
  
  /* Form group */
  .form-group {
    @apply mb-6;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1.5 transition-colors duration-300;
  }
  
  .form-error {
    @apply mt-1.5 text-sm text-red-600 transition-colors duration-300;
  }
}

/* Fix for mobile devices */
@media (max-width: 640px) {
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }
  
  .truncate-mobile {
    @apply truncate max-w-[250px];
  }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
  .desktop-container {
    @apply max-w-7xl mx-auto px-8;
  }
  
  .desktop-sidebar {
    @apply fixed top-0 left-0 h-full;
  }
  
  .desktop-content {
    @apply ml-64;
  }
}
